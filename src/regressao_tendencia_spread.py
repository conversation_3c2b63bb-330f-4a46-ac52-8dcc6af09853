#!/usr/bin/env python3
"""
Script para análise de regressão entre tendência e suas variáveis preditoras
Saída: Tendência atual (diferença de preços consecutivos)
Entradas: Tendência, Spread e Volume com atrasos de 1 a 5 dias (15 regressores)
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
import sys
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.model_selection import train_test_split
import seaborn as sns

# Adicionar o diretório src ao path para importar functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def obter_dados_para_regressao(ticker, nome):
    """
    Obtém dados históricos e prepara variáveis para regressão

    Returns:
        DataFrame com colunas: Tendencia_Atual + 15 regressores (Tendencia, Spread, Volume com lags 1-5)
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")

        stock = yf.Ticker(ticker)
        # Obter 15 meses para ter dados suficientes
        dados = stock.history(period="15mo")

        if dados.empty or len(dados) < 100:
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None

        # Calcular tendência (diferença de preços consecutivos)
        dados['Tendencia'] = dados['Close'].diff()
        
        # Obter spread usando edge_rolling
        try:
            spread_estimado = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=20)
            dados['Spread'] = spread_estimado * 100  # Converter para percentual
        except Exception:
            # Fallback: usar volatilidade como proxy do spread
            returns = dados['Close'].pct_change().dropna()
            volatilidade = returns.rolling(window=20).std()
            dados['Spread'] = volatilidade * 100
        
        # Preencher NaN no spread com média
        dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())

        # Normalizar volume (log para reduzir assimetria e padronizar)
        dados['Volume_Log'] = np.log(dados['Volume'] + 1)  # +1 para evitar log(0)
        dados['Volume_Normalizado'] = (dados['Volume_Log'] - dados['Volume_Log'].mean()) / dados['Volume_Log'].std()

        # Criar variáveis para regressão com múltiplos lags
        dados_regressao = pd.DataFrame()
        dados_regressao['Tendencia_Atual'] = dados['Tendencia']  # Y (saída)

        # Criar 15 regressores: Tendência, Spread e Volume com lags de 1 a 5 dias
        for lag in range(1, 6):  # lags de 1 a 5 dias
            dados_regressao[f'Tendencia_lag{lag}'] = dados['Tendencia'].shift(lag)
            dados_regressao[f'Spread_lag{lag}'] = dados['Spread'].shift(lag)
            dados_regressao[f'Volume_lag{lag}'] = dados['Volume_Normalizado'].shift(lag)
        
        # Remover linhas com NaN
        dados_regressao = dados_regressao.dropna()
        
        if len(dados_regressao) < 50:
            print(f"     ⚠️ Dados insuficientes após limpeza ({len(dados_regressao)} dias)")
            return None
        
        print(f"     ✅ {len(dados_regressao)} observações válidas para regressão")
        
        return dados_regressao, dados
        
    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def realizar_regressao(dados_regressao, ticker, nome):
    """
    Realiza regressão linear múltipla e retorna resultados
    """
    try:
        # Preparar dados - selecionar todas as colunas exceto a variável dependente
        feature_cols = [col for col in dados_regressao.columns if col != 'Tendencia_Atual']
        X = dados_regressao[feature_cols].values
        y = dados_regressao['Tendencia_Atual'].values
        
        # Dividir em treino e teste (80/20)
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Criar e treinar modelo
        modelo = LinearRegression()
        modelo.fit(X_train, y_train)
        
        # Fazer previsões
        y_pred_train = modelo.predict(X_train)
        y_pred_test = modelo.predict(X_test)
        
        # Calcular métricas
        r2_train = r2_score(y_train, y_pred_train)
        r2_test = r2_score(y_test, y_pred_test)
        mse_train = mean_squared_error(y_train, y_pred_train)
        mse_test = mean_squared_error(y_test, y_pred_test)
        mae_train = mean_absolute_error(y_train, y_pred_train)
        mae_test = mean_absolute_error(y_test, y_pred_test)
        
        # Coeficientes - armazenar todos os 15 coeficientes
        feature_cols = [col for col in dados_regressao.columns if col != 'Tendencia_Atual']
        coeficientes = dict(zip(feature_cols, modelo.coef_))
        intercepto = modelo.intercept_

        print(f"     📈 R² Treino: {r2_train:.4f} | R² Teste: {r2_test:.4f}")

        # Mostrar coeficientes mais significativos (top 3 por valor absoluto)
        coef_abs = {k: abs(v) for k, v in coeficientes.items()}
        top_coefs = sorted(coef_abs.items(), key=lambda x: x[1], reverse=True)[:3]
        coef_str = " | ".join([f"{k}: {coeficientes[k]:.4f}" for k, _ in top_coefs])
        print(f"     📊 Top 3 Coef.: {coef_str}")
        
        resultado = {
            'ticker': ticker,
            'nome': nome,
            'modelo': modelo,
            'r2_train': r2_train,
            'r2_test': r2_test,
            'mse_train': mse_train,
            'mse_test': mse_test,
            'mae_train': mae_train,
            'mae_test': mae_test,
            'coeficientes': coeficientes,  # Dicionário com todos os coeficientes
            'intercepto': intercepto,
            'feature_cols': feature_cols,  # Nomes das features
            'X_train': X_train,
            'X_test': X_test,
            'y_train': y_train,
            'y_test': y_test,
            'y_pred_train': y_pred_train,
            'y_pred_test': y_pred_test,
            'dados_regressao': dados_regressao
        }
        
        return resultado
        
    except Exception as e:
        print(f"     ❌ Erro na regressão: {e}")
        return None

def criar_grafico_regressao(resultado):
    """
    Cria gráficos de análise da regressão
    """
    ticker = resultado['ticker']
    nome = resultado['nome']
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Gráfico 1: Valores Reais vs Preditos (Teste)
    ax1.scatter(resultado['y_test'], resultado['y_pred_test'], alpha=0.6, s=30)
    
    # Linha de referência (predição perfeita)
    min_val = min(min(resultado['y_test']), min(resultado['y_pred_test']))
    max_val = max(max(resultado['y_test']), max(resultado['y_pred_test']))
    ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.7, label='Predição Perfeita')
    
    ax1.set_xlabel('Tendência Real (R$)')
    ax1.set_ylabel('Tendência Predita (R$)')
    ax1.set_title(f'Reais vs Preditos (Teste)\nR² = {resultado["r2_test"]:.4f}')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Gráfico 2: Resíduos vs Preditos
    residuos = resultado['y_test'] - resultado['y_pred_test']
    ax2.scatter(resultado['y_pred_test'], residuos, alpha=0.6, s=30)
    ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7)
    ax2.set_xlabel('Valores Preditos (R$)')
    ax2.set_ylabel('Resíduos (R$)')
    ax2.set_title('Análise de Resíduos')
    ax2.grid(True, alpha=0.3)
    
    # Gráfico 3: Top 10 Coeficientes Mais Importantes
    coefs_dict = resultado['coeficientes']
    coefs_abs = {k: abs(v) for k, v in coefs_dict.items()}
    top_coefs = sorted(coefs_abs.items(), key=lambda x: x[1], reverse=True)[:10]

    variaveis = [k for k, _ in top_coefs]
    coeficientes = [coefs_dict[k] for k, _ in top_coefs]
    cores = plt.cm.Set3(np.linspace(0, 1, len(variaveis)))
    
    bars = ax3.barh(range(len(variaveis)), coeficientes, color=cores, alpha=0.7)
    ax3.set_yticks(range(len(variaveis)))
    ax3.set_yticklabels(variaveis, fontsize=8)
    ax3.set_xlabel('Coeficiente')
    ax3.set_title('Top 10 Coeficientes Mais Importantes')
    ax3.grid(True, alpha=0.3, axis='x')

    # Adicionar valores nas barras
    for i, (bar, coef) in enumerate(zip(bars, coeficientes)):
        width = bar.get_width()
        ax3.text(width + (0.001 if width >= 0 else -0.001), bar.get_y() + bar.get_height()/2,
                f'{coef:.3f}', ha='left' if width >= 0 else 'right', va='center', fontsize=8)
    
    # Gráfico 4: Série Temporal das Tendências
    dados = resultado['dados_regressao']
    ax4.plot(dados.index[-100:], dados['Tendencia_Atual'].iloc[-100:], 
             label='Tendência Real', alpha=0.8, linewidth=1.5)
    
    # Fazer predições para os últimos 100 dias
    feature_cols = resultado['feature_cols']
    X_recent = dados[feature_cols].iloc[-100:].values
    y_pred_recent = resultado['modelo'].predict(X_recent)
    
    ax4.plot(dados.index[-100:], y_pred_recent, 
             label='Tendência Predita', alpha=0.8, linewidth=1.5, linestyle='--')
    
    ax4.set_xlabel('Data')
    ax4.set_ylabel('Tendência (R$)')
    ax4.set_title('Últimos 100 Dias: Real vs Predito')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # Título geral
    fig.suptitle(f'{nome} ({ticker.replace(".SA", "")}) - Análise de Regressão\n'
                f'Modelo: 15 Regressores (Tendência, Spread, Volume com lags 1-5 dias)\n'
                f'R² = {resultado["r2_test"]:.4f}',
                fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    
    # Salvar gráfico
    os.makedirs('results/figures/regressao_tendencia', exist_ok=True)
    nome_arquivo = f"results/figures/regressao_tendencia/regressao_{ticker.replace('.SA', '')}.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"     💾 {nome_arquivo}")
    
    return nome_arquivo

def gerar_relatorio_regressao(resultados):
    """
    Gera relatório detalhado da análise de regressão
    """
    print("\n" + "="*120)
    print("📈 RELATÓRIO ANÁLISE DE REGRESSÃO - 15 REGRESSORES (LAGS 1-5 DIAS)")
    print("="*120)
    print("Modelo: Tendência_Atual = β₀ + Σ(βᵢ×Variávelᵢ) onde i = 1..15")
    print("Variáveis: Tendência, Spread, Volume com lags de 1 a 5 dias")
    print("="*120)

    # Ordenar por R² de teste
    resultados_ord = sorted(resultados, key=lambda x: x['r2_test'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'R²_Teste':<8} {'R²_Treino':<9} {'MAE_Teste':<10} {'Top 3 Coeficientes':<40}")
    print("-" * 120)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['r2_test'] >= 0.1 else "🟡" if r['r2_test'] >= 0.05 else "🔴"

        # Pegar top 3 coeficientes por valor absoluto
        coefs_abs = {k: abs(v) for k, v in r['coeficientes'].items()}
        top_coefs = sorted(coefs_abs.items(), key=lambda x: x[1], reverse=True)[:3]
        top_coefs_str = ", ".join([f"{k}:{r['coeficientes'][k]:.3f}" for k, _ in top_coefs])

        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['r2_test']:>6.4f} "
              f"{r['r2_train']:>7.4f} {r['mae_test']:>8.4f} "
              f"{top_coefs_str:<40}")

    # Estatísticas gerais
    print("\n" + "="*120)
    print("📊 ESTATÍSTICAS GERAIS")
    print("="*120)

    r2_tests = [r['r2_test'] for r in resultados]
    r2_trains = [r['r2_train'] for r in resultados]

    print(f"R² Teste - Média: {np.mean(r2_tests):.4f} | Mediana: {np.median(r2_tests):.4f} | Desvio: {np.std(r2_tests):.4f}")
    print(f"R² Treino - Média: {np.mean(r2_trains):.4f} | Mediana: {np.median(r2_trains):.4f} | Desvio: {np.std(r2_trains):.4f}")

    # Análise dos coeficientes mais importantes
    print("\n📈 ANÁLISE DOS COEFICIENTES MAIS IMPORTANTES:")

    # Coletar todos os coeficientes de todas as ações
    all_coefs = {}
    for r in resultados:
        for var, coef in r['coeficientes'].items():
            if var not in all_coefs:
                all_coefs[var] = []
            all_coefs[var].append(coef)

    # Calcular estatísticas por variável
    for var in sorted(all_coefs.keys()):
        coefs = all_coefs[var]
        media = np.mean(coefs)
        mediana = np.median(coefs)
        positivos = sum(1 for c in coefs if c > 0)
        print(f"{var:<20}: Média={media:>7.4f} | Mediana={mediana:>7.4f} | Positivos={positivos:>2d}/{len(coefs)}")

    # Análise de qualidade do modelo
    print("\n" + "="*140)
    print("🎯 ANÁLISE DE QUALIDADE DOS MODELOS")
    print("="*140)

    bons_modelos = len([r for r in resultados if r['r2_test'] >= 0.1])
    modelos_razoaveis = len([r for r in resultados if 0.05 <= r['r2_test'] < 0.1])
    modelos_fracos = len([r for r in resultados if r['r2_test'] < 0.05])

    print(f"Modelos Bons (R² ≥ 0.10):     {bons_modelos:2d}/{len(resultados)} ({bons_modelos/len(resultados)*100:.1f}%)")
    print(f"Modelos Razoáveis (0.05-0.10): {modelos_razoaveis:2d}/{len(resultados)} ({modelos_razoaveis/len(resultados)*100:.1f}%)")
    print(f"Modelos Fracos (R² < 0.05):    {modelos_fracos:2d}/{len(resultados)} ({modelos_fracos/len(resultados)*100:.1f}%)")

    # Análise dos coeficientes
    print("\n" + "="*140)
    print("🔍 ANÁLISE DOS COEFICIENTES")
    print("="*140)

    coef_tend_positivos = len([r for r in resultados if r['coef_tendencia'] > 0])
    coef_spread_positivos = len([r for r in resultados if r['coef_spread'] > 0])
    coef_volume_positivos = len([r for r in resultados if r['coef_volume'] > 0])

    print(f"Coeficiente Tendência Positivo: {coef_tend_positivos:2d}/{len(resultados)} ({coef_tend_positivos/len(resultados)*100:.1f}%)")
    print(f"Coeficiente Spread Positivo:    {coef_spread_positivos:2d}/{len(resultados)} ({coef_spread_positivos/len(resultados)*100:.1f}%)")
    print(f"Coeficiente Volume Positivo:    {coef_volume_positivos:2d}/{len(resultados)} ({coef_volume_positivos/len(resultados)*100:.1f}%)")

    print("\nInterpretação:")
    print("• Coef. Tendência > 0: Tendência anterior influencia positivamente a atual (momentum)")
    print("• Coef. Tendência < 0: Tendência anterior influencia negativamente a atual (reversão)")
    print("• Coef. Spread > 0: Maior spread associado a maior volatilidade de tendência")
    print("• Coef. Spread < 0: Maior spread associado a menor volatilidade de tendência")
    print("• Coef. Volume > 0: Maior volume associado a maior variação de preço")
    print("• Coef. Volume < 0: Maior volume associado a menor variação de preço")

def criar_dashboard_regressao(resultados):
    """
    Cria dashboard consolidado com análise de todos os modelos
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Gráfico 1: Distribuição de R²
    r2_tests = [r['r2_test'] for r in resultados]
    ax1.hist(r2_tests, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(np.mean(r2_tests), color='red', linestyle='--', label=f'Média: {np.mean(r2_tests):.4f}')
    ax1.set_xlabel('R² de Teste')
    ax1.set_ylabel('Frequência')
    ax1.set_title('Distribuição de R² dos Modelos')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Gráfico 2: Coeficientes de Tendência vs Spread
    coef_tendencias = [r['coef_tendencia'] for r in resultados]
    coef_spreads = [r['coef_spread'] for r in resultados]

    scatter = ax2.scatter(coef_tendencias, coef_spreads,
                         c=r2_tests, cmap='viridis', alpha=0.7, s=60)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    ax2.set_xlabel('Coeficiente Tendência Anterior')
    ax2.set_ylabel('Coeficiente Spread')
    ax2.set_title('Relação entre Coeficientes')
    plt.colorbar(scatter, ax=ax2, label='R² Teste')
    ax2.grid(True, alpha=0.3)

    # Gráfico 3: R² Treino vs Teste (Overfitting)
    r2_trains = [r['r2_train'] for r in resultados]
    ax3.scatter(r2_trains, r2_tests, alpha=0.7, s=60)
    ax3.plot([0, max(max(r2_trains), max(r2_tests))],
             [0, max(max(r2_trains), max(r2_tests))],
             'r--', alpha=0.7, label='Linha Ideal')
    ax3.set_xlabel('R² Treino')
    ax3.set_ylabel('R² Teste')
    ax3.set_title('Análise de Overfitting')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Gráfico 4: Top 10 Melhores Modelos
    top_10 = sorted(resultados, key=lambda x: x['r2_test'], reverse=True)[:10]
    tickers = [r['ticker'].replace('.SA', '') for r in top_10]
    r2_values = [r['r2_test'] for r in top_10]

    bars = ax4.barh(range(len(tickers)), r2_values, alpha=0.7, color='lightgreen')
    ax4.set_yticks(range(len(tickers)))
    ax4.set_yticklabels(tickers)
    ax4.set_xlabel('R² de Teste')
    ax4.set_title('Top 10 Melhores Modelos')
    ax4.grid(True, alpha=0.3, axis='x')

    # Adicionar valores nas barras
    for i, (bar, r2) in enumerate(zip(bars, r2_values)):
        ax4.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
                f'{r2:.4f}', ha='left', va='center', fontsize=9)

    plt.tight_layout()

    # Salvar dashboard
    os.makedirs('results/figures/regressao_tendencia', exist_ok=True)
    dashboard_path = 'results/figures/regressao_tendencia/dashboard_regressao.png'
    plt.savefig(dashboard_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📊 Dashboard salvo: {dashboard_path}")

    return dashboard_path

def main():
    print("🚀 ANÁLISE DE REGRESSÃO - TENDÊNCIA vs TENDÊNCIA ANTERIOR + SPREAD + VOLUME")
    print("="*80)
    print("📊 Modelo de Regressão Linear Múltipla:")
    print("   • Variável Dependente (Y): Tendência atual (diferença de preços)")
    print("   • Variável Independente 1 (X₁): Tendência do dia anterior")
    print("   • Variável Independente 2 (X₂): Spread bid/ask do mesmo dia")
    print("   • Variável Independente 3 (X₃): Volume normalizado do mesmo dia")
    print("   • Objetivo: Prever tendência atual baseada em tendência passada, liquidez e volume")

    # Carregar ações do arquivo CSV
    acoes = carregar_acoes_diversificadas()

    if not acoes:
        print("❌ Não foi possível carregar as ações do arquivo CSV")
        return

    print(f"\n📋 Serão analisadas {len(acoes)} ações diversificadas")
    print("⏱️  Tempo estimado: 3-5 minutos")

    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return

    print(f"\n📈 Iniciando análise de regressão...")

    resultados = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome) in enumerate(acoes, 1):
        print(f"\n[{i:2d}/{len(acoes)}]", end=" ")

        # Obter dados para regressão
        resultado_dados = obter_dados_para_regressao(ticker, nome)

        if resultado_dados is not None:
            dados_regressao, dados_historicos = resultado_dados

            # Realizar regressão
            resultado_regressao = realizar_regressao(dados_regressao, ticker, nome)

            if resultado_regressao is not None:
                # Criar gráfico individual
                criar_grafico_regressao(resultado_regressao)
                resultados.append(resultado_regressao)
                sucesso += 1
            else:
                erro += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if resultados:
        # Gerar relatório
        gerar_relatorio_regressao(resultados)

        # Criar dashboard
        criar_dashboard_regressao(resultados)

        # Salvar resultados em CSV
        try:
            df_resultados = pd.DataFrame([{
                'ticker': r['ticker'],
                'nome': r['nome'],
                'r2_test': r['r2_test'],
                'r2_train': r['r2_train'],
                'mse_test': r['mse_test'],
                'mae_test': r['mae_test'],
                'coef_tendencia': r['coef_tendencia'],
                'coef_spread': r['coef_spread'],
                'coef_volume': r['coef_volume'],
                'intercepto': r['intercepto']
            } for r in resultados])

            os.makedirs('results/csv/regressao_tendencia', exist_ok=True)
            csv_path = 'results/csv/regressao_tendencia/resultados_regressao.csv'
            df_resultados.to_csv(csv_path, index=False)
            print(f"   • Resultados salvos em: {csv_path}")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

    else:
        print("\n❌ Nenhuma análise foi concluída!")

if __name__ == "__main__":
    main()
