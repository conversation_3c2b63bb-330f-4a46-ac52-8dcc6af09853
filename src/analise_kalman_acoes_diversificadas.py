#!/usr/bin/env python3
"""
Script para análise das 20 ações diversificadas usando Filtro de <PERSON>lman com Spread
Substitui a média móvel de 50 dias pela previsão do filtro de Kalman
Usa spread bid/ask como entrada de controle (indicador de liquidez)
Mantém a média móvel de 200 dias e faz previsão 20 dias à frente
"""

import yfinance as yf
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import warnings
import os
import sys
from kalman import KalmanFilter
from datetime import datetime, timedelta

# Adicionar o diretório src ao path para importar functions
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from functions import edge_rolling

warnings.filterwarnings('ignore')

# Configurar matplotlib
import matplotlib
matplotlib.use('Agg')  # Backend para salvar sem display

def carregar_acoes_diversificadas():
    """
    Carrega as 20 ações do arquivo CSV de diversificação
    """
    try:
        csv_path = 'results/csv/correlation_data/acoes_diversificacao.csv'
        df = pd.read_csv(csv_path)
        
        # Pegar apenas as primeiras 20 ações (excluindo linha vazia no final)
        acoes = []
        for _, row in df.head(20).iterrows():
            ticker = row['Ticker'] + '.SA'
            nome = row['Nome']
            acoes.append((ticker, nome))
        
        print(f"📋 Carregadas {len(acoes)} ações diversificadas do arquivo CSV")
        return acoes
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo CSV: {e}")
        return []

def configurar_filtro_kalman_com_spread(precos, spread):
    """
    Configura o filtro de Kalman para previsão de preços de ações usando spread
    Modelo: preço[t+1] = preço[t] + tendência[t] + spread_effect[t] + ruído
    Estado: [preço, tendência]
    Spread usado como entrada de controle (indicador de liquidez)
    """
    # Verificar e limpar dados de entrada
    precos_clean = pd.Series(precos).fillna(method='ffill').fillna(method='bfill')
    spread_clean = pd.Series(spread).fillna(method='ffill').fillna(method='bfill')

    # Verificar se ainda há NaN após limpeza
    if precos_clean.isna().any() or spread_clean.isna().any():
        raise ValueError("Dados contêm NaN após limpeza")

    # Normalizar spread para evitar problemas numéricos
    spread_mean = spread_clean.mean()
    spread_std = spread_clean.std()
    if spread_std == 0 or pd.isna(spread_std):
        spread_std = 1e-6
    spread_normalizado = (spread_clean)# - spread_mean) / spread_std

    # Matrizes do modelo de espaço de estados (2D: preço, tendência)
    F = np.array([[1, 1],    # preço[t+1] = preço[t] + tendência[t]
                  [0, 0.9]])   # tendência[t+1] = tendência[t]

    # Matriz de controle - spread influencia a volatilidade/incerteza
    B = np.array([[0],
                  [-0.7]])       # Reduzido para usar apenas spread


    H = np.array([[1, 0]])   # Observamos apenas o preço

    # Covariâncias (ajustadas para modelo com spread)
    Q = 0.1*np.eye(2)  # Ruído no processo (preço e tendência)

    R = np.array([[1.0]])           # Ruído na observação

    # Estado inicial
    x0 = np.array([[float(precos_clean.iloc[0])],     # Preço inicial
                   [0.0]])             # Tendência inicial zero

    P0 = np.eye(2)      # Covariância inicial


    kf = KalmanFilter(F, B, H, Q, R, x0, P0)

    return kf, spread_normalizado

def aplicar_filtro_kalman_com_spread(precos, spread):
    """
    Aplica o filtro de Kalman aos preços usando spread como entrada de controle
    """
    try:
        # Configurar filtro com spread
        kf, spread_normalizado = configurar_filtro_kalman_com_spread(precos, spread)

        # Aplicar filtro sequencialmente
        precos_filtrados = []
        estados = []
        covariancias = []

        for i, preco in enumerate(precos):
            # Verificar se o preço é válido
            if pd.isna(preco):
                print(f"     ⚠️ Preço NaN encontrado no índice {i}")
                continue

            # Entrada de controle baseada no spread normalizado
            spread_val = spread_normalizado.iloc[i] if hasattr(spread_normalizado, 'iloc') else spread_normalizado[i]
            if pd.isna(spread_val):
                spread_val = 0.0  # Valor padrão para spread NaN

            u = np.array([[float(spread_val)]])

            # Predição
            kf.predict(u)

            # Atualização com observação
            z = np.array([[float(preco)]])
            estado_atualizado = kf.update(z)

            # Armazenar resultados
            precos_filtrados.append(estado_atualizado[0, 0])  # Preço filtrado
            estados.append(estado_atualizado.copy())
            covariancias.append(kf.P.copy())

        precos_filtrados = np.array(precos_filtrados)

        return precos_filtrados, kf, estados, covariancias, spread_normalizado

    except Exception as e:
        print(f"     ⚠️ Erro no filtro de Kalman com spread: {e}")
        return None, None, None, None, None

def prever_kalman_com_spread(kf, ultimo_estado, ultima_covariancia, spread_medio_normalizado, spread_std, dias_previsao=20):
    """
    Faz previsão usando o filtro de Kalman com spread médio como entrada de controle
    """
    try:
        previsoes = []

        # Criar uma cópia do filtro para não alterar o original
        kf_pred = KalmanFilter(kf.F, kf.B, kf.H, kf.Q, kf.R, ultimo_estado.copy(), ultima_covariancia.copy())

        # Entrada de controle baseada no spread médio normalizado
        spread_base = float(spread_medio_normalizado) if not pd.isna(spread_medio_normalizado) else 0.0
        spread_noise = float(spread_std) if not pd.isna(spread_std) else 0.1

        u = np.array([[spread_base]])
        rng = np.random.default_rng()

        for _ in range(dias_previsao):
            # Adicionar ruído ao spread para simular variação
            spread_with_noise = spread_base + spread_noise * rng.normal()
            u_noisy = np.array([[spread_with_noise]])

            # Predição do próximo estado
            estado_pred = kf_pred.predict(u_noisy)

            # Previsão do preço (primeira componente do estado)
            preco_previsto = estado_pred[0, 0]
            previsoes.append(preco_previsto)

            # Para próxima iteração, usar o estado predito como base
            # (sem atualização, pois não temos observações futuras)

        return np.array(previsoes)

    except Exception as e:
        print(f"     ⚠️ Erro na previsão Kalman com spread: {e}")
        return None

def obter_dados_com_kalman_spread(ticker, nome, data_corte=None):
    """
    Obtém dados de 15 meses e aplica filtro de Kalman com spread*volume

    Args:
        ticker: Símbolo da ação
        nome: Nome da empresa
        data_corte: Data limite para usar nos dados (formato 'YYYY-MM-DD' ou datetime).
                   Se None, usa todos os dados disponíveis até hoje.
                   O filtro de Kalman usará apenas dados até esta data para fazer previsões.
    """
    try:
        print(f"  📊 {ticker.replace('.SA', ''):8s} - {nome}")

        stock = yf.Ticker(ticker)
        # Obter 15 meses para ter dados suficientes para MM200
        dados = stock.history(period="15mo")

        if dados.empty or len(dados) < 200:
            print(f"     ⚠️ Dados insuficientes ({len(dados) if not dados.empty else 0} dias)")
            return None

        # Guardar dados completos para exibição no gráfico
        dados_completos = dados.copy()

        # Aplicar corte de data se especificado (apenas para treinamento do Kalman)
        if data_corte is not None:
            if isinstance(data_corte, str):
                data_corte = pd.to_datetime(data_corte)

            # Garantir que a data de corte tenha o mesmo timezone dos dados
            if dados.index.tz is not None:
                if hasattr(data_corte, 'tz') and data_corte.tz is None:
                    data_corte = data_corte.tz_localize(dados.index.tz)
                elif not hasattr(data_corte, 'tz'):
                    # Se data_corte é datetime.datetime, converter para pandas Timestamp
                    data_corte = pd.Timestamp(data_corte).tz_localize(dados.index.tz)

            # Filtrar dados até a data de corte APENAS para treinamento
            dados_treino = dados[dados.index <= data_corte]

            if dados_treino.empty or len(dados_treino) < 200:
                print(f"     ⚠️ Dados insuficientes após corte de data ({len(dados_treino)} dias)")
                return None

            print(f"     📅 Treinamento até {data_corte.strftime('%Y-%m-%d')} ({len(dados_treino)} dias)")
            print(f"     📊 Exibindo dados completos até {dados_completos.index[-1].strftime('%Y-%m-%d')} ({len(dados_completos)} dias)")

            # Usar dados de treino para o Kalman
            dados = dados_treino
        else:
            print(f"     📅 Usando todos os dados disponíveis ({len(dados)} dias)")
            data_corte = None

        # Obter dados de bid/ask atuais para calcular spread médio
        try:
            info = stock.info
            bid_atual = info.get('bid')
            ask_atual = info.get('ask')

            if bid_atual and ask_atual and bid_atual > 0 and ask_atual > 0:
                spread_atual = ((ask_atual - bid_atual) / bid_atual) * 100
                print(f"     📈 Spread atual: {spread_atual:.4f}% (Bid: R${bid_atual:.2f}, Ask: R${ask_atual:.2f})")
            else:
                # Se não conseguir obter bid/ask, usar spread baseado na volatilidade
                returns = dados['Close'].pct_change().dropna()
                volatilidade = returns.rolling(window=20).std().iloc[-1]
                spread_atual = max(0.05, min(2.0, volatilidade * 100))  # Entre 0.05% e 2%
                print(f"     ⚠️ Bid/Ask não disponível. Usando spread baseado em volatilidade: {spread_atual:.4f}%")
        except Exception as e:
            # Em caso de erro, usar spread baseado na volatilidade
            try:
                returns = dados['Close'].pct_change().dropna()
                volatilidade = returns.rolling(window=20).std().iloc[-1]
                spread_atual = max(0.05, min(2.0, volatilidade * 100))  # Entre 0.05% e 2%
                print(f"     ⚠️ Erro ao obter bid/ask ({str(e)[:30]}). Usando spread baseado em volatilidade: {spread_atual:.4f}%")
            except:
                spread_atual = 0.1  # Spread padrão final
                print(f"     ⚠️ Usando spread padrão: {spread_atual:.4f}%")


        # Usar a função edge_rolling para estimar o spread bid/ask

        # A função edge_rolling espera colunas OHLC em maiúsculo
        spread_estimado = edge_rolling(dados[['Open', 'High', 'Low', 'Close']], window=20)
        # Converter para percentual e ajustar escala se necessário
        dados['Spread'] = spread_estimado  # Converter para percentual
        dados['Spread'] = dados['Spread'].fillna(spread_atual)  # Preencher NaN com spread base

        # Calcular spread multiplicado pelo volume (normalizado)
        # Normalizar volume para evitar valores muito grandes
        volume_normalizado = dados['Volume']# / dados['Volume'].rolling(window=20).mean()
        dados['SpreadVolume'] = dados['Spread'] * volume_normalizado
        dados['SpreadVolume'] = dados['SpreadVolume'].fillna(dados['Spread'])  # Fallback para spread simples


        # Calcular média móvel de 200 dias nos dados de treino
        dados['MM200'] = dados['Close'].rolling(window=200).mean()

        # Aplicar filtro de Kalman com spread nos dados de treino
        resultado_kalman = aplicar_filtro_kalman_com_spread(dados['Close'], dados['Spread'])

        if resultado_kalman[0] is None:
            return None

        precos_filtrados, kf, estados, covariancias, spread_normalizado = resultado_kalman
        dados['Kalman'] = precos_filtrados

        # Fazer previsão 20 dias à frente usando spread médio
        ultimo_estado = estados[-1]
        ultima_cov = covariancias[-1]
        spread_medio_normalizado = spread_normalizado[-60:].mean()
        spread_std_normalizado = spread_normalizado[-60:].std(ddof=1)
        previsoes = prever_kalman_com_spread(kf, ultimo_estado, ultima_cov,
                                             spread_medio_normalizado, spread_std_normalizado, 20)

        # Preparar dados completos para exibição
        if data_corte is not None:
            # Calcular Kalman e MM200 para dados completos (para visualização)
            dados_completos['Returns'] = dados_completos['Close'].pct_change()
            dados_completos['Volatility'] = dados_completos['Returns'].rolling(window=20).std()

            # Usar edge_rolling também para dados completos
            try:
                spread_completo = edge_rolling(dados_completos[['Open', 'High', 'Low', 'Close']], window=20)
                dados_completos['Spread'] = spread_completo * 100  # Converter para percentual
                dados_completos['Spread'] = dados_completos['Spread'].fillna(spread_atual)

                # Calcular spread*volume para dados completos
                volume_normalizado_completo = dados_completos['Volume'] / dados_completos['Volume'].rolling(window=20).mean()
                dados_completos['SpreadVolume'] = dados_completos['Spread'] * volume_normalizado_completo
                dados_completos['SpreadVolume'] = dados_completos['SpreadVolume'].fillna(dados_completos['Spread'])
            except Exception:
                # Fallback para método anterior se edge_rolling falhar
                vol_norm_completo = (dados_completos['Volatility'] - dados_completos['Volatility'].mean()) / dados_completos['Volatility'].std()
                dados_completos['Spread'] = spread_atual * (1 + vol_norm_completo * 0.5)
                dados_completos['Spread'] = dados_completos['Spread'].fillna(spread_atual)

                # Fallback para SpreadVolume
                volume_normalizado_completo = dados_completos['Volume'] / dados_completos['Volume'].rolling(window=20).mean()
                dados_completos['SpreadVolume'] = dados_completos['Spread'] * volume_normalizado_completo
                dados_completos['SpreadVolume'] = dados_completos['SpreadVolume'].fillna(dados_completos['Spread'])

            dados_completos['MM200'] = dados_completos['Close'].rolling(window=200).mean()

            # Para o Kalman nos dados completos, usar apenas até a data de corte para treino
            # e estender as previsões para o período posterior
            kalman_completo = np.full(len(dados_completos), np.nan)
            kalman_completo[:len(dados)] = precos_filtrados
            dados_completos['Kalman'] = kalman_completo

            # Pegar últimos 12 meses dos dados completos
            dados_12m = dados_completos.tail(252)
        else:
            # Sem corte, usar dados normalmente
            dados_12m = dados.tail(252)

        # Informar sobre previsões
        ultima_data_treino = dados.index[-1]
        if data_corte:
            print(f"     ✅ {len(dados_12m)} dias exibidos (com dados reais completos)")
            print(f"     📅 Previsões a partir de: {ultima_data_treino.strftime('%Y-%m-%d')}")
        else:
            print(f"     ✅ {len(dados_12m)} dias (com Kalman+Spread*Volume e MM200)")

        return dados_12m, previsoes, kf, data_corte

    except Exception as e:
        print(f"     ❌ Erro: {str(e)[:50]}")
        return None

def analisar_tendencia_kalman(dados):
    """
    Analisa a tendência baseada no filtro de Kalman e MM200
    """
    if dados is None or len(dados) < 50:
        return "Indefinida", "gray"

    preco_atual = dados['Close'].iloc[-1]
    kalman_atual = dados['Kalman'].iloc[-1]
    mm200_atual = dados['MM200'].iloc[-1]

    # Verificar se os valores estão válidos
    if pd.isna(kalman_atual) or pd.isna(mm200_atual):
        return "Indefinida", "gray"

    # Análise de tendência baseada em Kalman
    if preco_atual > kalman_atual > mm200_atual:
        return "Altista Forte", "darkgreen"
    elif preco_atual > kalman_atual and kalman_atual < mm200_atual:
        return "Altista Moderada", "green"
    elif preco_atual < kalman_atual < mm200_atual:
        return "Baixista Forte", "darkred"
    elif preco_atual < kalman_atual and kalman_atual > mm200_atual:
        return "Baixista Moderada", "red"
    else:
        return "Lateral", "orange"

def criar_grafico_kalman(ticker, nome, dados, previsoes, data_corte=None):
    """
    Cria gráfico com preço, filtro de Kalman, MM200 e previsões

    Args:
        ticker: Símbolo da ação
        nome: Nome da empresa
        dados: DataFrame com dados históricos completos
        previsoes: Array com previsões
        data_corte: Data de corte para marcar no gráfico (opcional)
    """
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

    # Calcular estatísticas
    preco_inicial = dados['Close'].iloc[0]
    preco_final = dados['Close'].iloc[-1]
    performance = ((preco_final / preco_inicial) - 1) * 100

    # Análise de tendência
    tendencia, cor_tendencia = analisar_tendencia_kalman(dados)

    # Gráfico 1: Preço + Kalman + MM200 + Previsões
    ax1.plot(dados.index, dados['Close'], linewidth=2.5, color='#1f77b4',
             label='Preço de Fechamento', zorder=3)

    # Filtro de Kalman com Spread (substitui MM50)
    ax1.plot(dados.index, dados['Kalman'], linewidth=2, color='orange',
             label='Filtro de Kalman + Spread', alpha=0.8, zorder=2)

    # Média móvel de 200 dias
    ax1.plot(dados.index, dados['MM200'], linewidth=2, color='red',
             label='MM 200 dias', alpha=0.8, zorder=2)

    # Área entre preço e Kalman
    ax1.fill_between(dados.index, dados['Close'], dados['Kalman'],
                     where=(dados['Close'] >= dados['Kalman']),
                     color='lightgreen', alpha=0.3, interpolate=True,
                     label='Preço > Kalman')
    ax1.fill_between(dados.index, dados['Close'], dados['Kalman'],
                     where=(dados['Close'] < dados['Kalman']),
                     color='lightcoral', alpha=0.3, interpolate=True,
                     label='Preço < Kalman')

    # Marcar data de corte se especificada
    if data_corte is not None:
        ax1.axvline(x=data_corte, color='red', linestyle='--', alpha=0.7, linewidth=2,
                   label='Data de Corte (Limite do Treinamento)', zorder=4)

    # Adicionar previsões se disponíveis
    if previsoes is not None and len(previsoes) > 0:
        # Determinar data de início das previsões
        if data_corte is not None:
            # Se há data de corte, previsões começam a partir dela
            data_inicio_previsao = data_corte
        else:
            # Sem data de corte, previsões começam após último dado
            data_inicio_previsao = dados.index[-1]

        # Criar datas futuras para as previsões
        datas_futuras = pd.date_range(start=data_inicio_previsao + timedelta(days=1),
                                     periods=len(previsoes), freq='D')

        # Plotar previsões
        ax1.plot(datas_futuras, previsoes, linewidth=2, color='purple',
                linestyle='--', label='Previsão Kalman (20 dias)', alpha=0.8, zorder=2)

        # Conectar ponto de início com primeira previsão
        if data_corte is not None:
            # Encontrar preço na data de corte ou mais próximo
            dados_ate_corte = dados[dados.index <= data_corte]
            if not dados_ate_corte.empty:
                preco_corte = dados_ate_corte['Close'].iloc[-1]
                ax1.plot([data_inicio_previsao, datas_futuras[0]],
                        [preco_corte, previsoes[0]],
                        linewidth=1, color='purple', linestyle=':', alpha=0.6)
        else:
            ax1.plot([data_inicio_previsao, datas_futuras[0]],
                    [dados['Close'].iloc[-1], previsoes[0]],
                    linewidth=1, color='purple', linestyle=':', alpha=0.6)

    # Título do gráfico
    titulo_base = f'{nome} ({ticker.replace(".SA", "")}) - Filtro de Kalman + Spread (12 Meses)'
    if data_corte is not None:
        titulo_base += f'\n🔴 Treinamento até {data_corte.strftime("%Y-%m-%d")} | Dados reais completos exibidos'

    ax1.set_title(titulo_base, fontsize=16, fontweight='bold')
    ax1.set_ylabel('Preço (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Estatísticas no gráfico
    kalman_atual = dados['Kalman'].iloc[-1] if not pd.isna(dados['Kalman'].iloc[-1]) else 0
    mm200_atual = dados['MM200'].iloc[-1] if not pd.isna(dados['MM200'].iloc[-1]) else 0

    stats_text = f'Preço: R$ {preco_final:.2f} | Performance: {performance:+.1f}%\n'
    stats_text += f'Kalman: R$ {kalman_atual:.2f} | MM200: R$ {mm200_atual:.2f}\n'
    stats_text += f'Tendência: {tendencia}'

    if previsoes is not None and len(previsoes) > 0:
        previsao_final = previsoes[-1]
        variacao_previsao = ((previsao_final / preco_final) - 1) * 100
        stats_text += f'\nPrevisão 20d: R$ {previsao_final:.2f} ({variacao_previsao:+.1f}%)'

    ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes,
             fontsize=10, verticalalignment='top',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9,
                      edgecolor=cor_tendencia, linewidth=2))

    # Gráfico 2: Spread (entrada do filtro de Kalman)
    ax2.plot(dados.index, dados['Spread'], alpha=0.7, color='purple', linewidth=1.5)
    ax2.set_title('Spread Bid/Ask (%) - Entrada do Filtro', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Spread (%)', fontsize=12)
    ax2.set_xlabel('Data', fontsize=12)
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()

    # Ensure directory exists
    os.makedirs('results/figures/kalman_analysis', exist_ok=True)

    # Salvar gráfico
    nome_arquivo = f"results/figures/kalman_analysis/kalman_{ticker.replace('.SA', '')}_12m.png"
    plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"     💾 {nome_arquivo}")

    resultado = {
        'ticker': ticker,
        'nome': nome,
        'performance': performance,
        'preco_atual': preco_final,
        'kalman': kalman_atual,
        'mm200': mm200_atual,
        'tendencia': tendencia,
        'cor_tendencia': cor_tendencia
    }

    if previsoes is not None and len(previsoes) > 0:
        resultado['previsao_20d'] = previsoes[-1]
        resultado['variacao_previsao'] = ((previsoes[-1] / preco_final) - 1) * 100

    return resultado


def gerar_relatorio_kalman(resultados):
    """
    Gera relatório detalhado da análise com Kalman
    """
    print("\n" + "="*110)
    print("📈 RELATÓRIO ANÁLISE TÉCNICA - FILTRO DE KALMAN + SPREAD + MM200")
    print("="*110)

    # Ordenar por performance
    resultados_ord = sorted(resultados, key=lambda x: x['performance'], reverse=True)

    print(f"{'#':<3} {'Ticker':<8} {'Nome':<20} {'Perf%':<8} {'Preço':<8} {'Kalman':<8} {'MM200':<8} {'Prev20d':<8} {'Tendência':<15}")
    print("-" * 110)

    for i, r in enumerate(resultados_ord, 1):
        emoji = "🟢" if r['performance'] >= 0 else "🔴"
        prev_text = f"{r.get('previsao_20d', 0):.2f}" if 'previsao_20d' in r else "N/A"
        print(f"{i:<3} {emoji} {r['ticker'].replace('.SA', ''):<6} "
              f"{r['nome'][:19]:<20} {r['performance']:>+6.1f}% "
              f"{r['preco_atual']:>6.2f} {r['kalman']:>6.2f} {r['mm200']:>6.2f} "
              f"{prev_text:>6s} {r['tendencia']:<15}")

    # Estatísticas por tendência
    print("\n" + "="*110)
    print("📊 ESTATÍSTICAS POR TENDÊNCIA")
    print("="*110)

    tendencias = {}
    for r in resultados:
        tend = r['tendencia']
        if tend not in tendencias:
            tendencias[tend] = []
        tendencias[tend].append(r['performance'])

    for tendencia, performances in tendencias.items():
        count = len(performances)
        media = np.mean(performances)
        print(f"{tendencia:<20}: {count:2d} ações | Performance média: {media:+6.1f}%")

    # Análise de posicionamento
    print("\n" + "="*110)
    print("🎯 ANÁLISE DE POSICIONAMENTO")
    print("="*110)

    acima_kalman = len([r for r in resultados if r['preco_atual'] > r['kalman']])
    acima_mm200 = len([r for r in resultados if r['preco_atual'] > r['mm200']])
    kalman_acima_mm200 = len([r for r in resultados if r['kalman'] > r['mm200']])

    print(f"Ações com preço acima do Kalman:  {acima_kalman:2d}/{len(resultados)} ({acima_kalman/len(resultados)*100:.1f}%)")
    print(f"Ações com preço acima da MM200:  {acima_mm200:2d}/{len(resultados)} ({acima_mm200/len(resultados)*100:.1f}%)")
    print(f"Ações com Kalman > MM200:       {kalman_acima_mm200:2d}/{len(resultados)} ({kalman_acima_mm200/len(resultados)*100:.1f}%)")

    # Análise de previsões
    previsoes_positivas = len([r for r in resultados if r.get('variacao_previsao', 0) > 0])
    if previsoes_positivas > 0:
        print(f"\n🔮 ANÁLISE DE PREVISÕES (20 DIAS)")
        print("="*110)
        print(f"Previsões positivas: {previsoes_positivas:2d}/{len(resultados)} ({previsoes_positivas/len(resultados)*100:.1f}%)")

        # Top 5 previsões mais otimistas
        with_predictions = [r for r in resultados if 'variacao_previsao' in r]
        top_predictions = sorted(with_predictions, key=lambda x: x['variacao_previsao'], reverse=True)[:5]

        print("\nTOP 5 Previsões Mais Otimistas:")
        for i, r in enumerate(top_predictions, 1):
            print(f"{i}. {r['ticker'].replace('.SA', ''):<6} - {r['nome'][:20]:<20} | "
                  f"Previsão: {r['variacao_previsao']:+6.1f}%")

def main():
    print("🚀 ANÁLISE COM FILTRO DE KALMAN + SPREAD - AÇÕES DIVERSIFICADAS")
    print("="*80)
    print("📊 Análise Técnica com:")
    print("   • Preço de fechamento")
    print("   • Filtro de Kalman com Spread (substitui MM50)")
    print("   • Média Móvel de 200 dias (MM200)")
    print("   • Spread bid/ask como entrada de controle")
    print("   • Previsão 20 dias à frente")
    print("   • Análise de tendência")
    print("   • Spread como indicador de liquidez")

    # Carregar ações do arquivo CSV
    acoes = carregar_acoes_diversificadas()

    if not acoes:
        print("❌ Não foi possível carregar as ações do arquivo CSV")
        return

    print(f"\n📋 Serão analisadas {len(acoes)} ações diversificadas")

    # Perguntar sobre data de corte para teste
    print("\n📅 CONFIGURAÇÃO DE DATA DE CORTE (para testes)")
    print("="*50)
    print("Você pode escolher uma data limite para o filtro de Kalman.")
    print("O filtro usará apenas dados até essa data para fazer previsões.")
    print("Isso é útil para testar a precisão das previsões.")
    print()
    print("Opções:")
    print("1. Usar todos os dados disponíveis (padrão)")
    print("2. Especificar uma data de corte")

    opcao_data = input("\nEscolha uma opção (1/2): ").strip()
    data_corte = None

    if opcao_data == '2':
        while True:
            try:
                data_input = input("Digite a data de corte (YYYY-MM-DD): ").strip()
                data_corte = pd.to_datetime(data_input)
                print(f"✅ Data de corte definida: {data_corte.strftime('%Y-%m-%d')}")
                print(f"   O filtro usará apenas dados até esta data para previsões.")
                break
            except:
                print("❌ Formato de data inválido. Use YYYY-MM-DD (ex: 2024-01-15)")
    else:
        print("✅ Usando todos os dados disponíveis (sem corte)")

    print("⏱️  Tempo estimado: 5-8 minutos (processamento Kalman + Spread)")

    confirma = input("\nDeseja continuar? (s/N): ").strip().lower()
    if confirma != 's':
        print("❌ Cancelado pelo usuário")
        return

    if data_corte:
        print(f"\n📈 Iniciando análise com filtro de Kalman + Spread (dados até {data_corte.strftime('%Y-%m-%d')})...")
    else:
        print(f"\n📈 Iniciando análise com filtro de Kalman + Spread...")

    resultados = []
    sucesso = 0
    erro = 0

    for i, (ticker, nome) in enumerate(acoes, 1):
        print(f"\n[{i:2d}/{len(acoes)}]", end=" ")

        resultado_dados = obter_dados_com_kalman_spread(ticker, nome, data_corte)

        if resultado_dados is not None:
            if len(resultado_dados) == 4:
                dados, previsoes, kf, data_corte_usada = resultado_dados
                resultado = criar_grafico_kalman(ticker, nome, dados, previsoes, data_corte_usada)
                resultados.append(resultado)
                sucesso += 1
            elif len(resultado_dados) == 3:
                # Compatibilidade com versão anterior
                dados, previsoes, kf = resultado_dados
                resultado = criar_grafico_kalman(ticker, nome, dados, previsoes)
                resultados.append(resultado)
                sucesso += 1
            else:
                erro += 1
        else:
            erro += 1

    print(f"\n✅ Processamento concluído!")
    print(f"   Sucessos: {sucesso}")
    print(f"   Erros: {erro}")

    if data_corte:
        print(f"   📅 Data de corte utilizada: {data_corte.strftime('%Y-%m-%d')}")
        print(f"   🔮 Previsões feitas a partir desta data")
    else:
        print(f"   📅 Dados completos utilizados (sem corte)")

    if resultados:
        # Criar dashboard


        # Gerar relatório
        gerar_relatorio_kalman(resultados)

        

        # Salvar resultados em CSV
        try:
            df_resultados = pd.DataFrame(resultados)
            os.makedirs('results/csv/kalman_analysis', exist_ok=True)
            csv_path = 'results/csv/kalman_analysis/resultados_kalman.csv'
            df_resultados.to_csv(csv_path, index=False)
            print(f"   • Resultados salvos em: {csv_path}")
        except Exception as e:
            print(f"   ⚠️ Erro ao salvar CSV: {e}")

    else:
        print("\n❌ Nenhum gráfico foi gerado!")

if __name__ == "__main__":
    main()
